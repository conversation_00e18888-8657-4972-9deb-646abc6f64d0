import { GridRenderCellParams } from '@mui/x-data-grid';
import DateRangeIcon from '@mui/icons-material/DateRange';
import { getDatePlusTimeString } from '../../../utils';
import { DeviationRead } from '../deviationTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

function DeviationDateCell(params: GridRenderCellParams<DeviationRead, number, string>) {
  const { value } = params;

  if (!value) {
    return null;
  }

  const dateString = getDatePlusTimeString(new Date(value));

  return (
    <Cell title={dateString}>
      <DateRangeIcon fontSize="small" />
      <CellText>{dateString}</CellText>
    </Cell>
  );
}

export default DeviationDateCell;
