import { Tab<PERSON>ontext, TabPanel } from '@mui/lab';
import { Box, Paper, Tab, Tabs } from '@mui/material';
import { DataGrid, GridColDef, GridPaginationModel, GridRenderCellParams, GridSortModel } from '@mui/x-data-grid';
import AddIcon from '@mui/icons-material/Add';
import { useEffect, useMemo, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import {
  InstructionColumn,
  InstructionColumnDefaults,
  InstructionColumnDisplayMap,
  InstructionFieldSortMap,
  InstructionParams,
  InstructionRead,
  InstructionSort,
  InstructionSortField,
  InstructionStatus,
} from './instructionTypes';
import { useAppDispatch, useAppSelector } from '../../store';
import { useGetInstructionsQuery } from './instructionApi';
import ErrorGate from '../../components/ErrorGate';
import PageTitle from '../title/Title';
import InstructionFilterBar from './InstructionFilterBar';
import { GuardResult } from '../guard/guardHooks';
import { PermissionType } from '../guard/guardTypes';
import ResponsiveButton from '../../components/ResponsiveButton';
import Guard from '../guard/Guard';
import { UserRole, UserDisplay } from '../user/userTypes';
import { setInstructionViewState } from './instructionSlice';
import { useGetCurrentUserQuery } from '../user/userApi';
import InstructionChipFilter from './InstructionChipFilter';
import { DataGridCellLinkWrapper } from '../../components/DataGridCellLink';
import GroupCell from '../../components/GroupCell';
import SidCell from '../../components/SidCell';
import UserCell from '../../components/UserCell';
import InstructionNameCell from './cell/InstructionNameCell';
import InstructionStatusCell from './cell/InstructionStatusCell';
import InstructionDateCell from './cell/InstructionDateCell';
import { GroupDisplay } from '../group/groupTypes';
import usePaging from '../../components/hooks/usePaging';
import NoRowsOverlay from '../../components/NoRowsOverlay';

const getInstructionUrl = (instructionId: number) => `${instructionId}`;

const getGridModelFromSort = (sort: InstructionSort[]): GridSortModel =>
  sort.map((s) => ({
    field: Object.keys(InstructionFieldSortMap).find((key) => InstructionFieldSortMap[key] === s.field) || s.field,
    sort: s.direction,
  }));

const getSortFromGridModel = (gridModel: GridSortModel): InstructionSort[] =>
  gridModel.map((model) => ({
    field: InstructionFieldSortMap[model.field] || (model.field as InstructionSortField),
    direction: model.sort || 'asc',
  }));

const columnDefaults: Record<InstructionColumn, GridColDef<InstructionRead>> = {
  [InstructionColumn.SID]: {
    field: InstructionColumn.SID,
    headerName: InstructionColumnDisplayMap[InstructionColumn.SID],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<InstructionRead, number, string>) =>
      DataGridCellLinkWrapper(SidCell(params), getInstructionUrl(params.row.id)),
  },
  [InstructionColumn.DESCRIPTION]: {
    field: InstructionColumn.DESCRIPTION,
    headerName: InstructionColumnDisplayMap[InstructionColumn.DESCRIPTION],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<InstructionRead, string, string>) =>
      DataGridCellLinkWrapper(InstructionNameCell(params), getInstructionUrl(params.row.id)),
  },
  [InstructionColumn.GROUP]: {
    field: InstructionColumn.GROUP,
    headerName: InstructionColumnDisplayMap[InstructionColumn.GROUP],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<InstructionRead, string, string>) =>
      DataGridCellLinkWrapper(GroupCell(params), getInstructionUrl(params.row.id)),
    valueGetter: (value: GroupDisplay) => (value ? value.name : ''),
  },
  [InstructionColumn.CREATED_BY]: {
    field: InstructionColumn.CREATED_BY,
    headerName: InstructionColumnDisplayMap[InstructionColumn.CREATED_BY],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<InstructionRead, string, string>) =>
      DataGridCellLinkWrapper(UserCell(params), getInstructionUrl(params.row.id)),
    valueGetter: (value: UserDisplay) => (value ? value.fullName : ''),
  },
  [InstructionColumn.STATUS]: {
    field: InstructionColumn.STATUS,
    headerName: InstructionColumnDisplayMap[InstructionColumn.STATUS],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<InstructionRead, InstructionStatus, string>) =>
      DataGridCellLinkWrapper(InstructionStatusCell(params), getInstructionUrl(params.row.id)),
  },
  [InstructionColumn.DATE]: {
    field: InstructionColumn.DATE,
    headerName: InstructionColumnDisplayMap[InstructionColumn.DATE],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<InstructionRead, number, string>) =>
      DataGridCellLinkWrapper(InstructionDateCell(params), getInstructionUrl(params.row.id)),
    valueGetter: (_value: number, row: InstructionRead) => row.startTime,
    valueFormatter: (value: number) => (value ? new Date(value).toDateString() : ''),
  },
};

function InstructionListPage() {
  const { groupId } = useParams();
  const { page, setPage, pageSize, setPageSize } = usePaging();

  const { data: me } = useGetCurrentUserQuery();
  const instructionViewState = useAppSelector((state) => state.instruction.instructionViewState);
  const [sortModel, setSortModel] = useState<GridSortModel>(getGridModelFromSort(instructionViewState?.sort || []));
  const dispatch = useAppDispatch();

  const columns = useMemo(() => {
    const cols = instructionViewState.columns ? instructionViewState.columns : InstructionColumnDefaults;
    return cols
      .filter((c) => !c.hidden)
      .map((c) => ({
        ...columnDefaults[c.column],
        width: c.width,
      }));
  }, [instructionViewState.columns]);

  const getCandidateGroups = () =>
    instructionViewState?.candidateGroups?.length && instructionViewState.candidateGroups.length > 0
      ? instructionViewState?.candidateGroups
      : undefined;

  const getFilter = (view?: 'mine' | 'all') => {
    const usedView = view || instructionViewState.listView;
    if (usedView === 'all') {
      return undefined;
    }
    return `statusNot=${InstructionStatus.CLOSED}&statusNot=${InstructionStatus.CANCELED}$`;
  };

  const getSortString = (sort: InstructionSort[]): string | undefined => {
    if (!sort || sort.length === 0) return undefined;
    return sort.map((s) => `${s.field}:${s.direction}`).join(',');
  };

  const [params, setParams] = useState<InstructionParams>({
    pathGroupId: Number(groupId),
    search: instructionViewState.search,
    groupId: instructionViewState?.group?.id,
    status: instructionViewState?.status,
    createdBy: instructionViewState?.createdBy?.id,
    filter: getFilter(),
    candidateGroups: getCandidateGroups(),
    sort: getSortString(instructionViewState?.sort || []),
    pageSize,
    pageNumber: page,
  });

  const { data, isLoading, error } = useGetInstructionsQuery(params);

  useEffect(() => {
    if (instructionViewState) {
      setParams((prev) => ({
        ...prev,
        search: instructionViewState?.search,
        groupId: instructionViewState?.group?.id,
        status: instructionViewState?.status,
        createdBy: instructionViewState.createdBy?.id,
        candidateGroups: getCandidateGroups(),
        sort: getSortString(instructionViewState?.sort || []),
        pageSize,
        pageNumber: page,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [instructionViewState, page, pageSize]);

  const onTabSwitch = (view: 'mine' | 'all') => {
    setPage(0);
    dispatch(
      setInstructionViewState({
        ...instructionViewState,
        listView: view,
      })
    );
  };

  const handlePaginationChange = (updatedModel: GridPaginationModel) => {
    setPageSize(updatedModel.pageSize);
    setPage(updatedModel.page);
  };

  const handleSortModelChange = (newSortModel: GridSortModel) => {
    setSortModel(newSortModel);
    const newSort = getSortFromGridModel(newSortModel);
    dispatch(
      setInstructionViewState({
        ...instructionViewState,
        sort: newSort,
      })
    );
  };

  const resetPageNumber = (): void => {
    handlePaginationChange({ page: 0, pageSize });
  };

  const canCreateInstruction = (guardResult: GuardResult) =>
    guardResult.hasRole(UserRole.TENANT_ADMIN) || guardResult.hasPermission(PermissionType.INSTRUCTION_CREATE);

  return (
    <ErrorGate error={error}>
      <PageTitle page="Instructions" />
      <TabContext value="0">
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Tabs value={instructionViewState.listView} sx={{ width: 'fit-content' }}>
            <Tab label="Open" value="mine" onClick={() => onTabSwitch('mine')} />
            <Tab label="All" value="all" onClick={() => onTabSwitch('all')} />
          </Tabs>
          <Guard hasAccess={canCreateInstruction}>
            <Box>
              <ResponsiveButton component={Link} to="add" variant="contained" size="large" endIcon={<AddIcon />}>
                Create instruction
              </ResponsiveButton>
            </Box>
          </Guard>
        </Box>
        <InstructionChipFilter me={me} resetPageNumber={resetPageNumber} />
        <InstructionFilterBar groupId={Number(groupId)} resetPageNumber={resetPageNumber} />
        <TabPanel
          sx={{
            px: 0,
            pt: 1,
            pb: 0,
          }}
          value="0"
        >
          <Paper elevation={4}>
            <Box
              sx={{
                height: 'calc(100vh - 269px)',
                overflow: 'hidden',
                '@media (max-height: 600px)': {
                  height: '100%',
                },
              }}
            >
              <DataGrid
                rows={data?.content || []}
                columns={columns}
                rowCount={data?.total || 0}
                loading={isLoading}
                disableColumnMenu
                pagination
                paginationMode="server"
                paginationModel={{ page, pageSize }}
                onPaginationModelChange={handlePaginationChange}
                sortingMode="server"
                sortModel={sortModel}
                onSortModelChange={handleSortModelChange}
                disableRowSelectionOnClick
                slots={{
                  noRowsOverlay: NoRowsOverlay,
                }}
                onColumnWidthChange={(columnParams) => {
                  const newViewState = { ...instructionViewState };
                  if (newViewState.columns) {
                    // Clone the columns array to avoid direct mutation.
                    const updatedColumns = [...newViewState.columns];
                    // Find the column to update.
                    const columnToUpdate = updatedColumns.find((c) => c.column === columnParams.colDef.field);
                    if (columnToUpdate) {
                      // Get the index of the column and update immutably.
                      const index = updatedColumns.indexOf(columnToUpdate);
                      updatedColumns[index] = { ...columnToUpdate, width: columnParams.width };
                    }
                    newViewState.columns = updatedColumns;
                  }
                  dispatch(setInstructionViewState(newViewState));
                }}
                slotProps={{
                  loadingOverlay: { variant: 'skeleton', noRowsVariant: 'skeleton' },
                  noRowsOverlay: { title: 'No instructions found' },
                }}
              />
            </Box>
          </Paper>
        </TabPanel>
      </TabContext>
    </ErrorGate>
  );
}

export default InstructionListPage;
