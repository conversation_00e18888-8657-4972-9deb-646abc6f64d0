import { Box } from '@mui/material';
import { GridRenderCellParams } from '@mui/x-data-grid';
import DateRangeIcon from '@mui/icons-material/DateRange';
import ArrowRightAltIcon from '@mui/icons-material/ArrowRightAlt';
import { InstructionRead } from '../instructionTypes';
import { getDatePlusTimeString } from '../../../utils';
import Cell from '../../../components/Cell';

function InstructionDateCell({ row: instruction }: GridRenderCellParams<InstructionRead, number, string>) {
  const startTimeString = getDatePlusTimeString(new Date(instruction.startTime));
  const endTimeString = instruction.endTime !== null
    ? getDatePlusTimeString(new Date(instruction.endTime))
    : 'until closed';
  const fullDateRange = `${startTimeString} - ${endTimeString}`;

  return (
    <Cell title={fullDateRange}>
      <Box display="flex" alignItems="center">
        <DateRangeIcon fontSize="small" sx={{ mr: 0.5 }} />
        <Box sx={{ mr: 1 }}>{startTimeString}</Box>
        <ArrowRightAltIcon fontSize="small" sx={{ mr: 0.5 }} />
        <Box>{endTimeString}</Box>
      </Box>
    </Cell>
  );
}

export default InstructionDateCell;
